"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, Minus, Navigation } from "lucide-react";
import { useState } from "react";

export function MapSection() {
  const [locationQuery, setLocationQuery] = useState("");

  const handleZoomIn = () => {
    console.log("Zoom in");
    // TODO: Implement zoom in functionality
  };

  const handleZoomOut = () => {
    console.log("Zoom out");
    // TODO: Implement zoom out functionality
  };

  const handleLocationSearch = () => {
    console.log("Searching location:", locationQuery);
    // TODO: Implement location search functionality
  };

  const handleMyLocation = () => {
    console.log("Get my location");
    // TODO: Implement geolocation functionality
  };

  return (
    <section className="@container flex flex-col h-full flex-1">
      <div className="flex flex-1 flex-col @[480px]:px-4 @[480px]:py-3">
        <div
          className="bg-cover bg-center flex min-h-[320px] flex-1 flex-col justify-between px-4 pb-4 pt-5 @[480px]:rounded-xl @[480px]:px-8 @[480px]:pb-6 @[480px]:pt-8"
          style={{
            backgroundImage: `url("https://lh3.googleusercontent.com/aida-public/AB6AXuDhpzUVgQaKsh3kJQieDYwsaP8Jyx8f8phDDtiw4_em7NGlViZAYSIZBQxozEGKUzGa3XY7wiWPQ5XUFsIYkhreH0ZyrfHA2mSrzu5zebvkQstBdrIGQeyRVLuaJHvRUd-J3HNAjY2vRC4HJU4V8DKJK4wTefgu6ahpYnTN5i8mfkWKoSaKrqDS1Ty0iUBscmn-rC8nBIe36RiMP09Xn-wjijmd02OUKYKXIPNMobBPyFoMeYdVledjBX-neVw69nVD2zkSJD8oz0Nq")`
          }}
          role="img"
          aria-label="Interactive map background"
        >
          {/* Location Search */}
          <div className="flex flex-col min-w-40 h-12">
            <div className="flex w-full flex-1 items-stretch rounded-xl h-full">
              <div className="text-muted-foreground flex border-none bg-card items-center justify-center pl-4 rounded-l-xl border-r-0">
                <Search className="h-6 w-6" />
              </div>
              <Input
                placeholder="Search for a location"
                value={locationQuery}
                onChange={(e) => setLocationQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleLocationSearch()}
                className="flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-foreground focus:outline-0 focus:ring-0 border-none bg-card focus:border-none h-full placeholder:text-muted-foreground px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
              />
            </div>
          </div>
          
          {/* Map Controls */}
          <div className="flex flex-col items-end gap-3">
            {/* Zoom Controls */}
            <div className="flex flex-col gap-0.5">
              <Button
                onClick={handleZoomIn}
                className="flex size-10 items-center justify-center rounded-t-full bg-card shadow-sm hover:shadow-md transition-shadow p-0"
                variant="ghost"
                aria-label="Zoom in"
              >
                <Plus className="h-6 w-6 text-foreground" />
              </Button>
              <Button
                onClick={handleZoomOut}
                className="flex size-10 items-center justify-center rounded-b-full bg-card shadow-sm hover:shadow-md transition-shadow p-0"
                variant="ghost"
                aria-label="Zoom out"
              >
                <Minus className="h-6 w-6 text-foreground" />
              </Button>
            </div>
            
            {/* My Location Button */}
            <Button
              onClick={handleMyLocation}
              className="flex size-10 items-center justify-center rounded-full bg-card shadow-sm hover:shadow-md transition-shadow p-0"
              variant="ghost"
              aria-label="Get my location"
            >
              <Navigation className="h-6 w-6 text-foreground scale-x-[-1]" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
