import { Card, CardContent } from "@/components/ui/card";

interface BusinessCardProps {
  name: string;
  description: string;
  imageUrl: string;
  className?: string;
}

export function BusinessCard({ name, description, imageUrl, className }: BusinessCardProps) {
  return (
    <Card className={`flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60 bg-card shadow-sm hover:shadow-md transition-shadow ${className || ""}`}>
      <div
        className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex flex-col"
        style={{ backgroundImage: `url(${imageUrl})` }}
        role="img"
        aria-label={`Image of ${name}`}
      />
      <CardContent className="p-0">
        <div>
          <p className="text-foreground text-base font-medium leading-normal">
            {name}
          </p>
          <p className="text-muted-foreground text-sm font-normal leading-normal">
            {description}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
