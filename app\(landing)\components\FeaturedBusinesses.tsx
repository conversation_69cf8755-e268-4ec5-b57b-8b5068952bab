"use client";

import { BusinessCard } from "./BusinessCard";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

// Fallback data for when Convex is not available or loading
const fallbackBusinesses = [
  {
    _id: "fallback-1" as any,
    name: "The Cozy Corner Cafe",
    description: "A charming cafe with a warm atmosphere.",
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuDanNGuzgepwuU-SRHcBo33f8hX6SQEehZLh87Tze7izia_TCsJ5Tdrb7G4w_WKzxksDVtqxTXDCUcnqO8hvxSOxja-kWFn6vmZz-wbf-nZlCHQ-TBehUqhzY6JOuZSwA5v7k0L3_ERg3dKSoa1L8eSM2araJtXdjsoZOdIYeXUFkU_NkcFMDpsUkrX9EfxYUgnkWgVbExAYtz-GrpdLKdUX0-NCe9D1gTaX_mqSrDSD6SGTlrFpPJJC5lAMJWivYpFQedbiFQllzdL"
  },
  {
    _id: "fallback-2" as any,
    name: "AutoFix Mechanics",
    description: "Expert auto repair services for all makes and models.",
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuB25r3GgIzGp5iWeBBDpX5lgPVfTiqABcz0BOx38jm66EWzJxOIJJCL64eYPyTZ15X8Xmb8GKwWbV8Zqz0BVWu9mXbKNDXXcqV6kyZmrTSeo_yx4mnX10j8zatWbFbulh4Om7VOsmOAMhsR-zCYfdu1q-5H7fbmxFRsN-QKJHH_5McmeVZVOsu98zuCn-15VvgDDJTfy6UbG36exIifYuvVICeoC1Yon0Kip0183piGYMQDaaCo_BjCzxwwGwQajekiDKXICubMsFyV"
  },
  {
    _id: "fallback-3" as any,
    name: "Glamour Hair Studio",
    description: "Modern salon offering the latest hair and beauty treatments.",
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuBaQxLwimoQDAnpgdfT3kKnzVe_t86Vin4Deab4mtfGrjThSy818wiK5zl5Zq-k3Bv6Fa45EdCHaTDLYE9b1Q8qhaaBUSAzZwjQUnyb_GETV4Se_RY263Rw9HlCwk6IzMtOjpW85N4RyIwrgyFpwsAi4IOqegegXz6BDmAFQM2UU6wJYjN8jQgOSmcq4W9kYcJLnO41yk2g-QnhKiEtQUEx1XSC6d0hwJzz1dMtBBAmEm_LYUNrBAhirwG824uvGrjqxcRVXlOV1uYn"
  },
  {
    _id: "fallback-4" as any,
    name: "Sweet Delights Bakery",
    description: "Artisan bakery with fresh, daily baked goods.",
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuD16viCbT97AZzVB_yKDUVmm-M9wqf9oZwPHDZGFQ3cpepLJT6qFIue0_sk1YHqBf57tbyV18jACkSWOiB6-zVWm7gt0wtm-jXzgV3KUj3h67lVlYA1eJEDh4YQ4y0YkGHqKC1wGSS7H5YUVKYNunRdLoWwkWzVZNA1Rrq4DKoiftTLkhPPX_Hcly2I-wRLrsh8KfbbYbV_4fIPioxnIDhDAR-H32ZR7znUd9QJwY5Chos5ScO8T-wKbZIZOPEXmRuriJxLRVIIu_2R"
  }
];

export function FeaturedBusinesses() {
  // Try to get featured businesses from Convex
  const featuredBusinesses = useQuery(api.businesses.getFeaturedBusinesses, { limit: 8 });

  // Use fallback data if Convex query is loading or fails
  const businessesToShow = featuredBusinesses || fallbackBusinesses;

  return (
    <section>
      <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-tight px-4 pb-3 pt-5">
        Featured Businesses
      </h2>
      <div className="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
        <div className="flex items-stretch p-4 gap-3">
          {businessesToShow.map((business) => (
            <BusinessCard
              key={business._id}
              name={business.name}
              description={business.description || ""}
              imageUrl={business.imageUrl || ""}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
