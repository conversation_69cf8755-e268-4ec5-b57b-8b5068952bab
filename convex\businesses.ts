/**
 * Convex Functions: Business Directory Homepage API
 * 
 * Specialized functions for the LocalBiz homepage including featured businesses,
 * popular categories, and search functionality.
 */

import { query } from "./_generated/server";
import { v } from "convex/values";

// Validators
const businessCardValidator = v.object({
  _id: v.id("listings"),
  name: v.string(),
  description: v.optional(v.string()),
  imageUrl: v.optional(v.string()),
  categories: v.array(v.id("categories")),
  location: v.object({
    lat: v.number(),
    lng: v.number(),
  }),
  address: v.object({
    line1: v.string(),
    city: v.string(),
    region: v.string(),
    postalCode: v.string(),
    country: v.string(),
  }),
  views: v.number(),
});

const categoryCardValidator = v.object({
  _id: v.id("categories"),
  name: v.string(),
  slug: v.string(),
  description: v.optional(v.string()),
  listingCount: v.number(),
  imageUrl: v.optional(v.string()),
});

/**
 * Get featured businesses for homepage
 * Returns top-rated or most viewed businesses
 */
export const getFeaturedBusinesses = query({
  args: { 
    limit: v.optional(v.number()),
    categoryId: v.optional(v.id("categories"))
  },
  returns: v.array(businessCardValidator),
  handler: async (ctx, args) => {
    const limit = args.limit || 8;
    
    // Get approved listings
    let query = ctx.db.query("listings")
      .withIndex("byStatus", (q) => q.eq("status", "approved"));
    
    let listings = await query.collect();
    
    // Filter by category if specified
    if (args.categoryId) {
      listings = listings.filter(listing => 
        listing.categories.includes(args.categoryId!)
      );
    }
    
    // Sort by views (most popular first)
    listings.sort((a, b) => b.views - a.views);
    
    // Take the top listings
    const featured = listings.slice(0, limit);
    
    // Transform to business card format
    return featured.map(listing => ({
      _id: listing._id,
      name: listing.name,
      description: listing.description,
      imageUrl: listing.images.length > 0 ? `/api/image/${listing.images[0]}` : undefined,
      categories: listing.categories,
      location: listing.location,
      address: listing.address,
      views: listing.views,
    }));
  },
});

/**
 * Get popular categories for homepage
 * Returns categories with the most listings
 */
export const getPopularCategories = query({
  args: { 
    limit: v.optional(v.number()),
    parentOnly: v.optional(v.boolean())
  },
  returns: v.array(categoryCardValidator),
  handler: async (ctx, args) => {
    const limit = args.limit || 6;
    const parentOnly = args.parentOnly !== false; // Default to true
    
    // Get active categories
    let query = ctx.db.query("categories")
      .withIndex("byActive", (q) => q.eq("isActive", true));
    
    let categories = await query.collect();
    
    // Filter to parent categories only if requested
    if (parentOnly) {
      categories = categories.filter(category => !category.parentId);
    }
    
    // Sort by listing count (most popular first)
    categories.sort((a, b) => b.listingCount - a.listingCount);
    
    // Take the top categories
    const popular = categories.slice(0, limit);
    
    // Transform to category card format
    return popular.map(category => ({
      _id: category._id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      listingCount: category.listingCount,
      imageUrl: `/api/category-image/${category.slug}`, // Placeholder for category images
    }));
  },
});

/**
 * Search businesses for homepage search functionality
 */
export const searchBusinesses = query({
  args: {
    query: v.string(),
    location: v.optional(v.object({
      lat: v.number(),
      lng: v.number(),
      radius: v.optional(v.number()), // in kilometers
    })),
    categoryId: v.optional(v.id("categories")),
    limit: v.optional(v.number()),
  },
  returns: v.array(businessCardValidator),
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    const searchTerm = args.query.toLowerCase().trim();
    
    if (!searchTerm) {
      return [];
    }
    
    // Get approved listings
    let listings = await ctx.db.query("listings")
      .withIndex("byStatus", (q) => q.eq("status", "approved"))
      .collect();
    
    // Apply text search filter
    listings = listings.filter(listing => 
      listing.name.toLowerCase().includes(searchTerm) ||
      listing.description?.toLowerCase().includes(searchTerm) ||
      listing.address.city.toLowerCase().includes(searchTerm) ||
      listing.address.region.toLowerCase().includes(searchTerm)
    );
    
    // Apply category filter
    if (args.categoryId) {
      listings = listings.filter(listing =>
        listing.categories.includes(args.categoryId!)
      );
    }
    
    // Apply location filter if provided
    if (args.location) {
      const radius = args.location.radius || 10; // Default 10km radius
      listings = listings.filter(listing => {
        const distance = calculateDistance(
          args.location!.lat,
          args.location!.lng,
          listing.location.lat,
          listing.location.lng
        );
        return distance <= radius;
      });
      
      // Sort by distance when location is provided
      listings.sort((a, b) => {
        const distanceA = calculateDistance(
          args.location!.lat,
          args.location!.lng,
          a.location.lat,
          a.location.lng
        );
        const distanceB = calculateDistance(
          args.location!.lat,
          args.location!.lng,
          b.location.lat,
          b.location.lng
        );
        return distanceA - distanceB;
      });
    } else {
      // Sort by relevance (views) when no location
      listings.sort((a, b) => b.views - a.views);
    }
    
    // Take the top results
    const results = listings.slice(0, limit);
    
    // Transform to business card format
    return results.map(listing => ({
      _id: listing._id,
      name: listing.name,
      description: listing.description,
      imageUrl: listing.images.length > 0 ? `/api/image/${listing.images[0]}` : undefined,
      categories: listing.categories,
      location: listing.location,
      address: listing.address,
      views: listing.views,
    }));
  },
});

/**
 * Get businesses near a location for map display
 */
export const getBusinessesNearLocation = query({
  args: {
    lat: v.number(),
    lng: v.number(),
    radius: v.optional(v.number()), // in kilometers
    categoryId: v.optional(v.id("categories")),
    limit: v.optional(v.number()),
  },
  returns: v.array(businessCardValidator),
  handler: async (ctx, args) => {
    const radius = args.radius || 5; // Default 5km radius
    const limit = args.limit || 50;
    
    // Get approved listings
    let listings = await ctx.db.query("listings")
      .withIndex("byStatus", (q) => q.eq("status", "approved"))
      .collect();
    
    // Apply category filter
    if (args.categoryId) {
      listings = listings.filter(listing =>
        listing.categories.includes(args.categoryId!)
      );
    }
    
    // Filter by location radius
    listings = listings.filter(listing => {
      const distance = calculateDistance(
        args.lat,
        args.lng,
        listing.location.lat,
        listing.location.lng
      );
      return distance <= radius;
    });
    
    // Sort by distance
    listings.sort((a, b) => {
      const distanceA = calculateDistance(args.lat, args.lng, a.location.lat, a.location.lng);
      const distanceB = calculateDistance(args.lat, args.lng, b.location.lat, b.location.lng);
      return distanceA - distanceB;
    });
    
    // Take the closest results
    const results = listings.slice(0, limit);
    
    // Transform to business card format
    return results.map(listing => ({
      _id: listing._id,
      name: listing.name,
      description: listing.description,
      imageUrl: listing.images.length > 0 ? `/api/image/${listing.images[0]}` : undefined,
      categories: listing.categories,
      location: listing.location,
      address: listing.address,
      views: listing.views,
    }));
  },
});

// Utility function to calculate distance between two points using Haversine formula
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
