"use client";

import { Card, CardContent } from "@/components/ui/card";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

// Fallback data for when Convex is not available or loading
const fallbackCategories = [
  {
    _id: "fallback-1" as any,
    name: "Restaurants",
    slug: "restaurants",
    listingCount: 0,
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuDmyPWFdYKKBXX3_ioMw4F7A6TVw7pI8ArMZfsieIEBXRRb_TRnV-H6c1uLebemK9hEpBIi8ZHeH0o0dQe6cGwGyAe6X9-KRWdHF9thegpfN_kUSX63wg9Vr_KQ_s_7IYUsCX1TnWK5ES-A2GUD0jWz0jX6HnHANwEnIGEeNDo9AO_4VpQJAGfQacmblLPY-pw4EbMYhGktYIlXY0-CYe6iXnzdfanO_eiaKLLVNxSzzGUhVIYckinXsaA4t4ojTU4p4TbDlufNI9ZS"
  },
  {
    _id: "fallback-2" as any,
    name: "Home Services",
    slug: "home-services",
    listingCount: 0,
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuC5mmF6rsg010ZMovdwGxRG2GA2AXlQjcq2ZPwTCTV6sCVS-mW6K0s63TGhkzed-Lvihqin5gcUE8q835FaCwYhmIzbQk2tAfmabOto7x-qMEPfsTlw22CFz_JA-43X1mN6rVXawWc5Y_25SJpDjXF_20B0s1azkZT4xm7O-MBHWCH2-2Fps2Nh6x5c2M8adPUL21G46PpNpC3CwIvKryknHVvQZBsHlUHp0P7425YFFCrxKjrtjMXcQFM8ccNx3uPNXR43Q_UyKaHV"
  },
  {
    _id: "fallback-3" as any,
    name: "Beauty & Spas",
    slug: "beauty-spas",
    listingCount: 0,
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuBvWDXPsppfC1z4o7bv_2B2AlcSOS7rYcvVobpvpDgE_B96-sHIv_kYrTASxZl3cb-1enuP74c7aGUB4i3boF5IcgV5aWm0R1kKfIPBWS7FjvQ6s_O0UPms3uziesufReV22N2fCUIzqY7wjDqP235l1xWwJCIiLae_AfsZG8Gr6GEpMKMFLJAXnVIwiPGoZ9HGWR4aUiW8PYGlrc416oOCNGDT--aXYm87EO_7qTXopYcMpBxTPOa29qxg_y0UydA0FRhKsaQdiz6t"
  },
  {
    _id: "fallback-4" as any,
    name: "Automotive",
    slug: "automotive",
    listingCount: 0,
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuAU5QAHA4PE9eNNvs0yjO3mS1mzs6p_3Nyxjmr9gTxV5pYBfQJs0DcBVcIBvv43JK5yj1PFWXK3fOr8_6y0qd3QxFo6jpTgAs2Tw0ylT60leU2rnHpEGz4vGY-Hg4EKmlwEeVhSv4iV3tcD18fl1SD9LhKGCP7K5T2B1ndC8QbU485guVytxswE_wObZi7U_l1lu-FEDrrnKVhZmhjoxNl-dF8bA0ytj9UYgdrhB9n5U-jqQVxF9QFXtnldTyTStEkFXa0yLGi7QZix"
  },
  {
    _id: "fallback-5" as any,
    name: "Health & Medical",
    slug: "health-medical",
    listingCount: 0,
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuDwlpvjAKwURjPR_afAmTcCkiSXHFDH2tVbMXiHKBkIouJDqeitmo_6C9eU2HzXsSZIEYD1BbLzE3iilbvicDbrthDEqtSeVIR8iRWn6LPEh5myXdlngtnQJAOaDD3NtWlcptmXi1g9rudD29krsC9NZFMxMCVCiEQ-snrwl0FmUTXD8Ncu3XJ7K3Jw76SqkEwbE0WT4ZEl-R8KPTiYNidFrETPh2dgDQopXY_z8zBYgQI8O_gmVfNZcEnwmk3hzOA9bnGIbiI4QYtq"
  },
  {
    _id: "fallback-6" as any,
    name: "Professional Services",
    slug: "professional-services",
    listingCount: 0,
    imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuAVNP6D6aKGejyRsrRRRK-PqHO1LfphtxCT7R7wugAYBTLi5tFoeBeQxmkxB2UpsqSEw61S3OPzlxOKmyhpsO7txR71r1Aq0GR2_UhGztxeYzxw7dD84Cz3hVslJBbAE6Jp4bmw8CclIcrje0s_Na2hOhVaFjQ_9hrLJBK92YMGm4S6y_ZEhUbAP5qgrLL4-qdnqfuewzKuG2eRsIrniKUmL6pAodRezF62WEOtxnlVYTZCZ3pvVvRs6qH8yXVh1aVyJVI6c5T2ShvB"
  }
];

export function PopularCategories() {
  // Try to get popular categories from Convex
  const popularCategories = useQuery(api.businesses.getPopularCategories, { limit: 6 });

  // Use fallback data if Convex query is loading or fails
  const categoriesToShow = popularCategories || fallbackCategories;

  return (
    <section>
      <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-tight px-4 pb-3 pt-5">
        Popular Categories
      </h2>
      <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
        {categoriesToShow.map((category) => (
          <Card
            key={category._id}
            className="flex flex-col gap-3 pb-3 bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer"
          >
            <div
              className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
              style={{ backgroundImage: `url(${category.imageUrl})` }}
              role="img"
              aria-label={`${category.name} category`}
            />
            <CardContent className="p-0 px-3">
              <p className="text-foreground text-base font-medium leading-normal">
                {category.name}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
}
