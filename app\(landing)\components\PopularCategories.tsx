"use client";

import { Card, CardContent } from "@/components/ui/card";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

export function PopularCategories() {
  // Get popular categories from Convex (now with mock data)
  const popularCategories = useQuery(api.businesses.getPopularCategories, { limit: 6 });

  if (!popularCategories) {
    return (
      <section>
        <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-tight px-4 pb-3 pt-5">
          Popular Categories
        </h2>
        <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
          {/* Loading skeleton */}
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="flex flex-col gap-3 pb-3 bg-muted animate-pulse">
              <div className="w-full aspect-square bg-muted-foreground/20 rounded-xl" />
              <CardContent className="p-0 px-3">
                <div className="h-4 bg-muted-foreground/20 rounded w-3/4" />
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    );
  }

  return (
    <section>
      <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-tight px-4 pb-3 pt-5">
        Popular Categories
      </h2>
      <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
        {popularCategories.map((category) => (
          <Card
            key={category._id}
            className="flex flex-col gap-3 pb-3 bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer"
          >
            <div
              className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
              style={{ backgroundImage: `url(${category.imageUrl})` }}
              role="img"
              aria-label={`${category.name} category`}
            />
            <CardContent className="p-0 px-3">
              <p className="text-foreground text-base font-medium leading-normal">
                {category.name}
              </p>
              <p className="text-muted-foreground text-xs">
                {category.listingCount} listings
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
}
